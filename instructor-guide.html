<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instructor Guide - Advanced Web Development</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #06b6d4;
            --accent-color: #f59e0b;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-card: #ffffff;
            --border-color: #e2e8f0;
            --shadow-primary: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-card: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 3rem 0;
            margin-bottom: 3rem;
            border-radius: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .navigation {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color);
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .nav-item {
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--border-color);
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-item:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-card);
        }

        .nav-item i {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .nav-item:hover i {
            color: white;
        }

        .slide-section {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color);
        }

        .slide-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        .slide-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .slide-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .content-section {
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid var(--border-color);
        }

        .content-section h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .timing-info {
            background: linear-gradient(135deg, var(--accent-color), #ef4444);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .key-points {
            list-style: none;
            padding: 0;
        }

        .key-points li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
            position: relative;
            padding-left: 2rem;
        }

        .key-points li:before {
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--primary-color);
            font-size: 0.8rem;
        }

        .code-example {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .tips-box {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(6, 182, 212, 0.1));
            border: 1px solid var(--secondary-color);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .tips-box h5 {
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(245, 158, 11, 0.1));
            border: 1px solid #ef4444;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .warning-box h5 {
            color: #ef4444;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .resources-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .resource-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .resource-item:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chalkboard-teacher"></i> Instructor Guide</h1>
            <p>Advanced Web Development - University College of Management & Sciences</p>
            <p><i class="fas fa-clock"></i> Total Duration: 90-120 minutes | <i class="fas fa-users"></i> Audience: Intermediate to Advanced</p>
        </div>

        <div class="navigation">
            <h3 style="margin-bottom: 1.5rem; color: var(--primary-color);"><i class="fas fa-map"></i> Quick Navigation</h3>
            <div class="nav-grid">
                <a href="#slide-1" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Welcome Page</span>
                </a>
                <a href="#slide-2" class="nav-item">
                    <i class="fas fa-play"></i>
                    <span>Title Slide</span>
                </a>
                <a href="#slide-3" class="nav-item">
                    <i class="fas fa-globe"></i>
                    <span>What is Web Dev</span>
                </a>
                <a href="#slide-4" class="nav-item">
                    <i class="fas fa-palette"></i>
                    <span>Frontend Tech</span>
                </a>
                <a href="#slide-5" class="nav-item">
                    <i class="fas fa-code"></i>
                    <span>HTML Example</span>
                </a>
                <a href="#slide-6" class="nav-item">
                    <i class="fas fa-paint-brush"></i>
                    <span>CSS Example</span>
                </a>
                <a href="#slide-7" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>JavaScript Example</span>
                </a>
                <a href="#slide-8" class="nav-item">
                    <i class="fas fa-server"></i>
                    <span>Backend Tech</span>
                </a>
            </div>
        </div>

        <!-- Slide 1: Welcome Page -->
        <div id="slide-1" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">1</div>
                <h3 class="slide-title">Welcome Page - University Introduction</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 30 seconds (Auto-advance) | <i class="fas fa-eye"></i> Purpose: Brand Recognition & Credibility
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> What to Say (Word-for-Word Script)</h4>
                <div class="code-example" style="background: #1e3a8a; color: white; font-family: 'Space Grotesk', sans-serif;">
"Good morning/afternoon everyone! Welcome to our Advanced Web Development course.

As you can see from our welcome screen, this course is brought to you by the University College of Management & Sciences, in partnership with NAVTTC - the National Vocational and Technical Training Commission.

This partnership means that the certification you'll receive at the end of this course is not just from our university, but is also recognized by the government of Pakistan through NAVTTC. This adds significant value to your professional credentials.

Our university has been at the forefront of technology education, and today we're going to dive deep into the world of modern web development.

Let's begin our journey into building the future of the web!"
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Delivery Tips</h5>
                <ul class="key-points">
                    <li>Speak with confidence and enthusiasm</li>
                    <li>Make eye contact with students</li>
                    <li>Pause after mentioning NAVTTC partnership</li>
                    <li>Use hand gestures to point to logos on screen</li>
                </ul>
            </div>
        </div>

        <!-- Slide 2: Title Slide -->
        <div id="slide-2" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">2</div>
                <h3 class="slide-title">Title Slide - Course Introduction</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 2-3 minutes | <i class="fas fa-eye"></i> Purpose: Course Overview & Engagement
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> Detailed Speaking Script</h4>
                <div class="code-example" style="background: #1e3a8a; color: white; font-family: 'Space Grotesk', sans-serif;">
"Welcome to Advanced Web Development Fundamentals! Now, you might be wondering - is this advanced or fundamentals? Great question!

This course covers fundamental concepts, but we approach them from an advanced perspective. We won't just learn HTML tags - we'll understand semantic markup, accessibility, and modern best practices.

Before we dive in, let me ask you a few questions:
- How many of you have written HTML before? [Wait for hands]
- Who has used CSS to style a webpage? [Wait for hands]
- And JavaScript - who has written any JavaScript code? [Wait for hands]

Excellent! This gives me a good sense of where we're starting.

Now, this course assumes you have basic familiarity with these technologies. If you've never written HTML before, I recommend checking out our prerequisite materials first.

Today, we'll cover:
- Modern frontend technologies and best practices
- Backend development concepts
- Industry-standard tools and workflows
- Real-world development scenarios

This isn't just theory - we'll see practical examples and discuss how these concepts apply in actual web development jobs.

Are you ready to become professional web developers? Let's get started!"
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interaction Guidelines</h5>
                <ul class="key-points">
                    <li>Wait for actual responses to questions</li>
                    <li>Acknowledge different experience levels positively</li>
                    <li>If many lack prerequisites, adjust content accordingly</li>
                    <li>Create an inclusive environment for all skill levels</li>
                </ul>
            </div>
        </div>

        <!-- Slide 3: What is Web Development -->
        <div id="slide-3" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">3</div>
                <h3 class="slide-title">What is Web Development? - Foundation Concepts</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 5-7 minutes | <i class="fas fa-eye"></i> Purpose: Conceptual Framework
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> Complete Speaking Script</h4>
                <div class="code-example" style="background: #1e3a8a; color: white; font-family: 'Space Grotesk', sans-serif;">
"So, what exactly IS web development? Let me start with a simple definition:

Web development is the process of building and maintaining websites and web applications that run on the internet.

But let's break this down further. Think about the websites you use every day - Facebook, Google, YouTube, Amazon. Each of these represents thousands of hours of web development work.

[CLICK to reveal first point]
'Creating websites and web applications' - This includes everything from simple static websites to complex applications like online banking systems or e-commerce platforms.

[CLICK to reveal second point]
'Frontend Development' - This is what users see and interact with. It's the client-side - running in your browser. Think of it as the 'face' of the application.

'Backend Development' - This is the server-side logic that users don't see. It handles data processing, user authentication, database operations. It's the 'brain' of the application.

[CLICK to reveal third point]
'User Interface + Server Logic + Database' - These three components work together. The UI collects user input, the server processes it, and the database stores it.

Let me give you a real example: When you post on Facebook:
1. Frontend: You type your post and click 'Share'
2. Backend: Server validates your post, processes it
3. Database: Your post is stored and made available to your friends

This is the complete web development ecosystem working together!"
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Engagement Techniques</h5>
                <ul class="key-points">
                    <li>Use familiar websites as examples</li>
                    <li>Ask "What happens when you..." questions</li>
                    <li>Draw simple diagrams on whiteboard if available</li>
                    <li>Pause between fragments for questions</li>
                </ul>
            </div>
        </div>

        <!-- Slide 4: Frontend Technologies -->
        <div id="slide-4" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">4</div>
                <h3 class="slide-title">Frontend Technologies - The Building Blocks</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 8-10 minutes | <i class="fas fa-eye"></i> Purpose: Core Technology Understanding
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> Detailed Explanation Script</h4>
                <div class="code-example" style="background: #1e3a8a; color: white; font-family: 'Space Grotesk', sans-serif;">
"Now let's dive into the core frontend technologies. These are the building blocks of everything users see and interact with on the web.

[CLICK to reveal HTML card]
First, we have HTML - HyperText Markup Language. Think of HTML as the skeleton of a webpage. It provides structure and semantic meaning to content.

HTML isn't just about tags - it's about creating meaningful, accessible content. Modern HTML5 gives us semantic elements like &lt;article&gt;, &lt;section&gt;, and &lt;nav&gt; that help both browsers and screen readers understand our content better.

[CLICK to reveal CSS card]
Next is CSS - Cascading Style Sheets. If HTML is the skeleton, CSS is the skin, muscles, and clothing. It controls visual presentation, layout, and responsive design.

CSS has evolved tremendously. We now have powerful layout systems like CSS Grid and Flexbox, custom properties (CSS variables), and advanced features like animations and transforms. CSS isn't just about making things 'pretty' - it's crucial for user experience, accessibility, and even website performance.

[CLICK to reveal JavaScript card]
Finally, JavaScript - the programming language that adds interactivity, dynamic behavior, and client-side logic.

JavaScript has transformed from a simple scripting language to a powerful programming language. Modern JavaScript (ES6+) includes features like arrow functions, destructuring, async/await, and modules. It's what makes websites interactive and responsive to user actions.

Here's the key: These three technologies work together, but they have distinct responsibilities - we call this 'separation of concerns'. HTML for structure, CSS for presentation, JavaScript for behavior."
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interactive Demo Idea</h5>
                <p>Show a website and ask students to identify what each technology contributes. Point to text (HTML), colors/layout (CSS), and interactive elements (JavaScript).</p>
            </div>
        </div>

        <!-- Slide 5: HTML Example -->
        <div id="slide-5" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">5</div>
                <h3 class="slide-title">HTML Example - Structure in Action</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 6-8 minutes | <i class="fas fa-eye"></i> Purpose: Practical HTML Understanding
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> HTML Code Explanation Script</h4>
                <div class="code-example" style="background: #1e3a8a; color: white; font-family: 'Space Grotesk', sans-serif;">
"Let's look at a real HTML example and break it down line by line.

[Point to the code on screen]

First line: &lt;!DOCTYPE html&gt; - This tells the browser we're using HTML5. It's essential for modern web standards.

Next: &lt;html&gt; - This is our root element, containing everything else.

The &lt;head&gt; section contains metadata - information ABOUT the page, not content that users see:
- &lt;title&gt; sets what appears in the browser tab
- This is also where we'd link CSS files and add meta tags

The &lt;body&gt; section contains the actual content users see:
- &lt;h1&gt; is our main heading - there should only be one per page for SEO
- &lt;p&gt; is a paragraph element

Notice the proper nesting and indentation - this makes code readable and maintainable.

This might look simple, but this structure is the foundation of every website you've ever visited - from Google to Facebook to Amazon. They all start with this basic HTML structure.

The beauty of HTML is its simplicity and semantic meaning. Screen readers, search engines, and other tools can understand the structure and purpose of our content because we use meaningful HTML elements."
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interactive Teaching Moment</h5>
                <p>Ask students: "What would happen if we removed the DOCTYPE?" or "Why do you think we separate head and body?" This encourages critical thinking.</p>
            </div>
        </div>

        <!-- Slide 6: CSS Example -->
        <div id="slide-6" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">6</div>
                <h3 class="slide-title">CSS Example - Styling and Layout</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 8-10 minutes | <i class="fas fa-eye"></i> Purpose: CSS Practical Application
            </div>

            <div class="content-grid">
                <div class="content-section">
                    <h4><i class="fas fa-palette"></i> CSS Concepts Covered</h4>
                    <ul class="key-points">
                        <li>Selectors and specificity</li>
                        <li>Box model (margin, padding)</li>
                        <li>Typography and color</li>
                        <li>Layout techniques</li>
                    </ul>
                </div>

                <div class="content-section">
                    <h4><i class="fas fa-mobile-alt"></i> Responsive Design</h4>
                    <ul class="key-points">
                        <li>Max-width for responsive containers</li>
                        <li>Margin auto for centering</li>
                        <li>Relative units (em, rem, %)</li>
                        <li>Mobile-first approach</li>
                    </ul>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Demonstration Idea</h5>
                <p>Show the same HTML with and without CSS. Then apply styles one by one to show the transformation. Use browser dev tools to toggle styles on/off.</p>
            </div>

            <div class="warning-box">
                <h5><i class="fas fa-exclamation-triangle"></i> Common CSS Pitfalls</h5>
                <p>Discuss CSS specificity issues, the importance of consistent naming conventions, and why inline styles should be avoided in production code.</p>
            </div>
        </div>

        <!-- Slide 7: JavaScript Example -->
        <div id="slide-7" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">7</div>
                <h3 class="slide-title">JavaScript Example - Adding Interactivity</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 10-12 minutes | <i class="fas fa-eye"></i> Purpose: JavaScript Fundamentals
            </div>

            <div class="content-grid">
                <div class="content-section">
                    <h4><i class="fas fa-cogs"></i> JavaScript Concepts</h4>
                    <ul class="key-points">
                        <li>DOM manipulation methods</li>
                        <li>Event handling</li>
                        <li>Functions and scope</li>
                        <li>Modern ES6+ syntax</li>
                    </ul>
                </div>

                <div class="content-section">
                    <h4><i class="fas fa-rocket"></i> Advanced Topics</h4>
                    <ul class="key-points">
                        <li>Event delegation</li>
                        <li>Async programming</li>
                        <li>Error handling</li>
                        <li>Performance considerations</li>
                    </ul>
                </div>
            </div>

            <div class="content-section">
                <h4><i class="fas fa-code"></i> Code Explanation</h4>
                <div class="code-example">
// Function Declaration vs Expression
function greetUser() { ... }  // Hoisted
const greetUser = () => { ... }  // Not hoisted, modern syntax

// DOM Ready Event
document.addEventListener('DOMContentLoaded', function() {
    // Code runs after DOM is fully loaded
    // Better than window.onload for performance
});
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interactive Demo</h5>
                <p>Create a simple HTML page with an input field and button. Show how JavaScript can read the input, process it, and update the page dynamically.</p>
            </div>
        </div>

        <!-- Slide 8: Backend Technologies -->
        <div id="slide-8" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">8</div>
                <h3 class="slide-title">Backend Technologies - Server-Side Power</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 12-15 minutes | <i class="fas fa-eye"></i> Purpose: Backend Ecosystem Overview
            </div>

            <div class="content-grid">
                <div class="content-section">
                    <h4><i class="fas fa-server"></i> Server-Side Languages</h4>
                    <ul class="key-points">
                        <li>Node.js: JavaScript everywhere</li>
                        <li>Python: Django, Flask, FastAPI</li>
                        <li>PHP: WordPress, Laravel</li>
                        <li>Java: Enterprise applications</li>
                    </ul>
                </div>

                <div class="content-section">
                    <h4><i class="fas fa-database"></i> Database Technologies</h4>
                    <ul class="key-points">
                        <li>SQL vs NoSQL differences</li>
                        <li>PostgreSQL: Advanced features</li>
                        <li>MongoDB: Document-based</li>
                        <li>Redis: Caching and sessions</li>
                    </ul>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Career Guidance</h5>
                <p>Discuss job market trends. Node.js and Python are currently in high demand. Mention that learning one backend language well is better than knowing many superficially.</p>
            </div>

            <div class="content-section">
                <h4><i class="fas fa-plug"></i> API Development</h4>
                <ul class="key-points">
                    <li>REST API principles</li>
                    <li>GraphQL advantages</li>
                    <li>Authentication methods</li>
                    <li>API documentation importance</li>
                </ul>
            </div>
        </div>

        <div class="slide-section" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white;">
            <div style="text-align: center; padding: 2rem;">
                <h2><i class="fas fa-graduation-cap"></i> Instructor Guide Complete</h2>
                <p style="font-size: 1.2rem; margin: 1rem 0;">This guide covers the first 8 slides of your presentation.</p>
                <p>For additional slides, continue with the same detailed approach, focusing on:</p>
                <ul style="list-style: none; padding: 0; margin: 2rem 0;">
                    <li><i class="fas fa-check"></i> Clear learning objectives</li>
                    <li><i class="fas fa-check"></i> Interactive elements</li>
                    <li><i class="fas fa-check"></i> Real-world examples</li>
                    <li><i class="fas fa-check"></i> Common pitfalls to address</li>
                </ul>
                <p><strong>Remember:</strong> Adapt timing based on student engagement and questions!</p>
            </div>
        </div>
</body>
</html>
