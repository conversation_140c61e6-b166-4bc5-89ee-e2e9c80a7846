<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instructor Guide - Advanced Web Development</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&family=Noto+Nastaliq+Urdu:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #06b6d4;
            --accent-color: #f59e0b;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-card: #ffffff;
            --border-color: #e2e8f0;
            --shadow-primary: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-card: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 3rem 0;
            margin-bottom: 3rem;
            border-radius: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .language-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            margin: 1rem 0;
            display: inline-flex;
            gap: 0.5rem;
        }

        .lang-btn {
            background: transparent;
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .lang-btn.active {
            background: white;
            color: var(--primary-color);
        }

        .urdu-text {
            font-family: 'Noto Nastaliq Urdu', 'Jameel Noori Nastaleeq', serif;
            direction: rtl;
            text-align: right;
            line-height: 1.8;
        }

        .english-text {
            font-family: 'Space Grotesk', sans-serif;
            direction: ltr;
            text-align: left;
        }

        .bilingual-script {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .script-section {
            background: #1e3a8a;
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            border: 2px solid;
        }

        .english-script {
            border-color: #3b82f6;
        }

        .urdu-script {
            border-color: #10b981;
        }

        .script-header {
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }

        .navigation {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 3rem;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color);
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .nav-item {
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 10px;
            border: 1px solid var(--border-color);
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .nav-item:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-card);
        }

        .nav-item i {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .nav-item:hover i {
            color: white;
        }

        .slide-section {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color);
        }

        .slide-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }

        .slide-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .slide-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .content-section {
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid var(--border-color);
        }

        .content-section h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .timing-info {
            background: linear-gradient(135deg, var(--accent-color), #ef4444);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .key-points {
            list-style: none;
            padding: 0;
        }

        .key-points li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
            position: relative;
            padding-left: 2rem;
        }

        .key-points li:before {
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--primary-color);
            font-size: 0.8rem;
        }

        .code-example {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 10px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .tips-box {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(6, 182, 212, 0.1));
            border: 1px solid var(--secondary-color);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .tips-box h5 {
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(245, 158, 11, 0.1));
            border: 1px solid #ef4444;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .warning-box h5 {
            color: #ef4444;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .resources-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .resource-item {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .resource-item:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chalkboard-teacher"></i> Instructor Guide / استاد کی رہنمائی</h1>
            <p>Advanced Web Development - University College of Management & Sciences</p>
            <p>ایڈوانسڈ ویب ڈیولپمنٹ - یونیورسٹی کالج آف منیجمنٹ اینڈ سائنسز</p>
            <p><i class="fas fa-clock"></i> Total Duration: 90-120 minutes | کل وقت: ۹۰-۱۲۰ منٹ</p>
            <p><i class="fas fa-users"></i> Audience: Intermediate to Advanced | سامعین: درمیانی سے اعلیٰ درجے</p>

            <div class="language-toggle">
                <button class="lang-btn active" onclick="showLanguage('both')">Both Languages / دونوں زبانیں</button>
                <button class="lang-btn" onclick="showLanguage('english')">English Only</button>
                <button class="lang-btn" onclick="showLanguage('urdu')">اردو فقط</button>
            </div>
        </div>

        <div class="navigation">
            <h3 style="margin-bottom: 1.5rem; color: var(--primary-color);"><i class="fas fa-map"></i> Quick Navigation</h3>
            <div class="nav-grid">
                <a href="#slide-1" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Welcome Page</span>
                </a>
                <a href="#slide-2" class="nav-item">
                    <i class="fas fa-play"></i>
                    <span>Title Slide</span>
                </a>
                <a href="#slide-3" class="nav-item">
                    <i class="fas fa-globe"></i>
                    <span>What is Web Dev</span>
                </a>
                <a href="#slide-4" class="nav-item">
                    <i class="fas fa-palette"></i>
                    <span>Frontend Tech</span>
                </a>
                <a href="#slide-5" class="nav-item">
                    <i class="fas fa-code"></i>
                    <span>HTML Example</span>
                </a>
                <a href="#slide-6" class="nav-item">
                    <i class="fas fa-paint-brush"></i>
                    <span>CSS Example</span>
                </a>
                <a href="#slide-7" class="nav-item">
                    <i class="fas fa-bolt"></i>
                    <span>JavaScript Example</span>
                </a>
                <a href="#slide-8" class="nav-item">
                    <i class="fas fa-server"></i>
                    <span>Backend Tech</span>
                </a>
            </div>
        </div>

        <!-- Slide 1: Welcome Page -->
        <div id="slide-1" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">1</div>
                <h3 class="slide-title">Welcome Page - University Introduction</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 30 seconds (Auto-advance) | <i class="fas fa-eye"></i> Purpose: Brand Recognition & Credibility
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> What to Say (Word-for-Word Script) / کیا کہنا ہے (لفظ بہ لفظ اسکرپٹ)</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"Good morning/afternoon everyone! Welcome to our Advanced Web Development course.

As you can see from our welcome screen, this course is brought to you by the University College of Management & Sciences, in partnership with NAVTTC - the National Vocational and Technical Training Commission.

This partnership means that the certification you'll receive at the end of this course is not just from our university, but is also recognized by the government of Pakistan through NAVTTC. This adds significant value to your professional credentials.

Our university has been at the forefront of technology education, and today we're going to dive deep into the world of modern web development.

Let's begin our journey into building the future of the web!"
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"صبح بخیر / دوپہر بخیر سب کو! ہمارے ایڈوانسڈ ویب ڈیولپمنٹ کورس میں خوش آمدید۔

جیسا کہ آپ ہماری خوش آمدیدی اسکرین سے دیکھ سکتے ہیں، یہ کورس یونیورسٹی کالج آف منیجمنٹ اینڈ سائنسز کی جانب سے پیش کیا جا رہا ہے، NAVTTC - نیشنل ووکیشنل اینڈ ٹیکنیکل ٹریننگ کمیشن کے ساتھ شراکت میں۔

اس شراکت کا مطلب یہ ہے کہ اس کورس کے اختتام پر آپ کو جو سرٹیفیکیٹ ملے گا وہ نہ صرف ہماری یونیورسٹی کا ہے، بلکہ پاکستان کی حکومت کی جانب سے NAVTTC کے ذریعے بھی تسلیم شدہ ہے۔ یہ آپ کی پیشہ ورانہ اسناد میں خاص اہمیت کا اضافہ کرتا ہے۔

ہماری یونیورسٹی ٹیکنالوجی کی تعلیم میں پیش قدم رہی ہے، اور آج ہم جدید ویب ڈیولپمنٹ کی دنیا میں گہرائی سے جانے والے ہیں۔

آئیے ویب کا مستقبل بنانے کا سفر شروع کرتے ہیں!"
                        </div>
                    </div>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Delivery Tips / پیش کش کے نکات</h5>
                <ul class="key-points">
                    <li><strong>English:</strong> Speak with confidence and enthusiasm | <strong>اردو:</strong> اعتماد اور جوش کے ساتھ بولیں</li>
                    <li><strong>English:</strong> Make eye contact with students | <strong>اردو:</strong> طلباء سے آنکھوں کا رابطہ رکھیں</li>
                    <li><strong>English:</strong> Pause after mentioning NAVTTC partnership | <strong>اردو:</strong> NAVTTC شراکت کا ذکر کرنے کے بعد رک جائیں</li>
                    <li><strong>English:</strong> Use hand gestures to point to logos on screen | <strong>اردو:</strong> اسکرین پر لوگو کی طرف اشارہ کرنے کے لیے ہاتھ کے اشارے استعمال کریں</li>
                </ul>
            </div>
        </div>

        <!-- Slide 2: Title Slide -->
        <div id="slide-2" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">2</div>
                <h3 class="slide-title">Title Slide - Course Introduction</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 2-3 minutes | <i class="fas fa-eye"></i> Purpose: Course Overview & Engagement
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> Detailed Speaking Script / تفصیلی بولنے کا اسکرپٹ</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"Welcome to Advanced Web Development Fundamentals! Now, you might be wondering - is this advanced or fundamentals? Great question!

This course covers fundamental concepts, but we approach them from an advanced perspective. We won't just learn HTML tags - we'll understand semantic markup, accessibility, and modern best practices.

Before we dive in, let me ask you a few questions:
- How many of you have written HTML before? [Wait for hands]
- Who has used CSS to style a webpage? [Wait for hands]
- And JavaScript - who has written any JavaScript code? [Wait for hands]

Excellent! This gives me a good sense of where we're starting.

Now, this course assumes you have basic familiarity with these technologies. If you've never written HTML before, I recommend checking out our prerequisite materials first.

Today, we'll cover:
- Modern frontend technologies and best practices
- Backend development concepts
- Industry-standard tools and workflows
- Real-world development scenarios

This isn't just theory - we'll see practical examples and discuss how these concepts apply in actual web development jobs.

Are you ready to become professional web developers? Let's get started!"
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"ایڈوانسڈ ویب ڈیولپمنٹ فنڈامینٹلز میں خوش آمدید! اب آپ سوچ رہے ہوں گے - یہ ایڈوانسڈ ہے یا فنڈامینٹلز؟ بہترین سوال!

یہ کورس بنیادی تصورات کا احاطہ کرتا ہے، لیکن ہم انہیں ایڈوانسڈ نقطہ نظر سے دیکھتے ہیں۔ ہم صرف HTML ٹیگز نہیں سیکھیں گے - ہم سیمینٹک مارک اپ، رسائی، اور جدید بہترین طریقوں کو سمجھیں گے۔

شروع کرنے سے پہلے، میں آپ سے کچھ سوالات پوچھتا ہوں:
- آپ میں سے کتنوں نے پہلے HTML لکھا ہے؟ [ہاتھ اٹھانے کا انتظار کریں]
- کس نے ویب صفحے کو اسٹائل کرنے کے لیے CSS استعمال کیا ہے؟ [ہاتھ اٹھانے کا انتظار کریں]
- اور JavaScript - کس نے کوئی JavaScript کوڈ لکھا ہے؟ [ہاتھ اٹھانے کا انتظار کریں]

بہترین! یہ مجھے اچھا اندازہ دیتا ہے کہ ہم کہاں سے شروع کر رہے ہیں۔

اب، یہ کورس فرض کرتا ہے کہ آپ کو ان ٹیکنالوجیز کی بنیادی واقفیت ہے۔ اگر آپ نے کبھی HTML نہیں لکھا، تو میں پہلے ہمارے پیش قدمی مواد کو دیکھنے کی تجویز کرتا ہوں۔

آج ہم یہ موضوعات کا احاطہ کریں گے:
- جدید فرنٹ اینڈ ٹیکنالوجیز اور بہترین طریقے
- بیک اینڈ ڈیولپمنٹ کے تصورات
- انڈسٹری معیاری ٹولز اور ورک فلوز
- حقیقی دنیا کے ڈیولپمنٹ منظرنامے

یہ صرف نظریہ نہیں ہے - ہم عملی مثالیں دیکھیں گے اور بحث کریں گے کہ یہ تصورات حقیقی ویب ڈیولپمنٹ کی نوکریوں میں کیسے لاگو ہوتے ہیں۔

کیا آپ پیشہ ور ویب ڈیولپرز بننے کے لیے تیار ہیں؟ آئیے شروع کرتے ہیں!"
                        </div>
                    </div>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interaction Guidelines</h5>
                <ul class="key-points">
                    <li>Wait for actual responses to questions</li>
                    <li>Acknowledge different experience levels positively</li>
                    <li>If many lack prerequisites, adjust content accordingly</li>
                    <li>Create an inclusive environment for all skill levels</li>
                </ul>
            </div>
        </div>

        <!-- Slide 3: What is Web Development -->
        <div id="slide-3" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">3</div>
                <h3 class="slide-title">What is Web Development? - Foundation Concepts</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 5-7 minutes | <i class="fas fa-eye"></i> Purpose: Conceptual Framework
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> Complete Speaking Script / مکمل بولنے کا اسکرپٹ</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"So, what exactly IS web development? Let me start with a simple definition:

Web development is the process of building and maintaining websites and web applications that run on the internet.

But let's break this down further. Think about the websites you use every day - Facebook, Google, YouTube, Amazon. Each of these represents thousands of hours of web development work.

[CLICK to reveal first point]
'Creating websites and web applications' - This includes everything from simple static websites to complex applications like online banking systems or e-commerce platforms.

[CLICK to reveal second point]
'Frontend Development' - This is what users see and interact with. It's the client-side - running in your browser. Think of it as the 'face' of the application.

'Backend Development' - This is the server-side logic that users don't see. It handles data processing, user authentication, database operations. It's the 'brain' of the application.

[CLICK to reveal third point]
'User Interface + Server Logic + Database' - These three components work together. The UI collects user input, the server processes it, and the database stores it.

Let me give you a real example: When you post on Facebook:
1. Frontend: You type your post and click 'Share'
2. Backend: Server validates your post, processes it
3. Database: Your post is stored and made available to your friends

This is the complete web development ecosystem working together!"
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"تو، ویب ڈیولپمنٹ دراصل کیا ہے؟ آئیے ایک سادہ تعریف سے شروع کرتے ہیں:

ویب ڈیولپمنٹ انٹرنیٹ پر چلنے والی ویب سائٹس اور ویب ایپلیکیشنز بنانے اور ان کی دیکھ بھال کا عمل ہے۔

لیکن آئیے اسے مزید تفصیل سے سمجھتے ہیں۔ ان ویب سائٹس کے بارے میں سوچیں جو آپ روزانہ استعمال کرتے ہیں - فیس بک، گوگل، یوٹیوب، ایمیزون۔ ان میں سے ہر ایک ہزاروں گھنٹوں کے ویب ڈیولپمنٹ کام کی نمائندگی کرتا ہے۔

[پہلا نکتہ ظاہر کرنے کے لیے کلک کریں]
'ویب سائٹس اور ویب ایپلیکیشنز بنانا' - اس میں سادہ جامد ویب سائٹس سے لے کر پیچیدہ ایپلیکیشنز جیسے آن لائن بینکنگ سسٹم یا ای کامرس پلیٹ فارم تک سب کچھ شامل ہے۔

[دوسرا نکتہ ظاہر کرنے کے لیے کلک کریں]
'فرنٹ اینڈ ڈیولپمنٹ' - یہ وہ ہے جو صارف دیکھتے اور اس کے ساتھ تعامل کرتے ہیں۔ یہ کلائنٹ سائیڈ ہے - آپ کے براؤزر میں چلتا ہے۔ اسے ایپلیکیشن کا 'چہرہ' سمجھیں۔

'بیک اینڈ ڈیولپمنٹ' - یہ سرور سائیڈ لاجک ہے جو صارف نہیں دیکھتے۔ یہ ڈیٹا پروسیسنگ، صارف کی تصدیق، ڈیٹابیس آپریشنز کو سنبھالتا ہے۔ یہ ایپلیکیشن کا 'دماغ' ہے۔

[تیسرا نکتہ ظاہر کرنے کے لیے کلک کریں]
'صارف انٹرفیس + سرور لاجک + ڈیٹابیس' - یہ تینوں اجزاء مل کر کام کرتے ہیں۔ UI صارف کا ان پٹ جمع کرتا ہے، سرور اسے پروسیس کرتا ہے، اور ڈیٹابیس اسے محفوظ کرتا ہے۔

آئیے ایک حقیقی مثال دیتے ہیں: جب آپ فیس بک پر پوسٹ کرتے ہیں:
1. فرنٹ اینڈ: آپ اپنی پوسٹ ٹائپ کرتے ہیں اور 'شیئر' پر کلک کرتے ہیں
2. بیک اینڈ: سرور آپ کی پوسٹ کی تصدیق کرتا ہے، اسے پروسیس کرتا ہے
3. ڈیٹابیس: آپ کی پوسٹ محفوظ ہوتی ہے اور آپ کے دوستوں کے لیے دستیاب ہوتی ہے

یہ مکمل ویب ڈیولپمنٹ ایکو سسٹم مل کر کام کر رہا ہے!"
                        </div>
                    </div>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Engagement Techniques</h5>
                <ul class="key-points">
                    <li>Use familiar websites as examples</li>
                    <li>Ask "What happens when you..." questions</li>
                    <li>Draw simple diagrams on whiteboard if available</li>
                    <li>Pause between fragments for questions</li>
                </ul>
            </div>
        </div>

        <!-- Slide 4: Frontend Technologies -->
        <div id="slide-4" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">4</div>
                <h3 class="slide-title">Frontend Technologies - The Building Blocks</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 8-10 minutes | <i class="fas fa-eye"></i> Purpose: Core Technology Understanding
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> Detailed Explanation Script / تفصیلی وضاحتی اسکرپٹ</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"Now let's dive into the core frontend technologies. These are the building blocks of everything users see and interact with on the web.

[CLICK to reveal HTML card]
First, we have HTML - HyperText Markup Language. Think of HTML as the skeleton of a webpage. It provides structure and semantic meaning to content.

HTML isn't just about tags - it's about creating meaningful, accessible content. Modern HTML5 gives us semantic elements like &lt;article&gt;, &lt;section&gt;, and &lt;nav&gt; that help both browsers and screen readers understand our content better.

[CLICK to reveal CSS card]
Next is CSS - Cascading Style Sheets. If HTML is the skeleton, CSS is the skin, muscles, and clothing. It controls visual presentation, layout, and responsive design.

CSS has evolved tremendously. We now have powerful layout systems like CSS Grid and Flexbox, custom properties (CSS variables), and advanced features like animations and transforms. CSS isn't just about making things 'pretty' - it's crucial for user experience, accessibility, and even website performance.

[CLICK to reveal JavaScript card]
Finally, JavaScript - the programming language that adds interactivity, dynamic behavior, and client-side logic.

JavaScript has transformed from a simple scripting language to a powerful programming language. Modern JavaScript (ES6+) includes features like arrow functions, destructuring, async/await, and modules. It's what makes websites interactive and responsive to user actions.

Here's the key: These three technologies work together, but they have distinct responsibilities - we call this 'separation of concerns'. HTML for structure, CSS for presentation, JavaScript for behavior."
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"اب آئیے بنیادی فرنٹ اینڈ ٹیکنالوجیز میں گہرائی سے جاتے ہیں۔ یہ ہر اس چیز کے بنیادی اجزاء ہیں جو صارف ویب پر دیکھتے اور اس کے ساتھ تعامل کرتے ہیں۔

[HTML کارڈ ظاہر کرنے کے لیے کلک کریں]
پہلے، ہمارے پاس HTML ہے - ہائپر ٹیکسٹ مارک اپ لینگویج۔ HTML کو ویب صفحے کے کنکال کے طور پر سوچیں۔ یہ مواد کو ڈھانچہ اور معنی خیز معنی فراہم کرتا ہے۔

HTML صرف ٹیگز کے بارے میں نہیں ہے - یہ معنی خیز، قابل رسائی مواد بنانے کے بارے میں ہے۔ جدید HTML5 ہمیں معنی خیز عناصر جیسے &lt;article&gt;, &lt;section&gt;, اور &lt;nav&gt; فراہم کرتا ہے جو براؤزرز اور اسکرین ریڈرز دونوں کو ہمارے مواد کو بہتر طریقے سے سمجھنے میں مدد کرتے ہیں۔

[CSS کارڈ ظاہر کرنے کے لیے کلک کریں]
اگلا CSS ہے - کیسکیڈنگ اسٹائل شیٹس۔ اگر HTML کنکال ہے، تو CSS جلد، پٹھے، اور کپڑے ہیں۔ یہ بصری پیشکش، لے آؤٹ، اور ریسپانسو ڈیزائن کو کنٹرول کرتا ہے۔

CSS نے بہت ترقی کی ہے۔ اب ہمارے پاس طاقتور لے آؤٹ سسٹم جیسے CSS Grid اور Flexbox، کسٹم پراپرٹیز (CSS variables)، اور ایڈوانسڈ فیچرز جیسے animations اور transforms ہیں۔ CSS صرف چیزوں کو 'خوبصورت' بنانے کے بارے میں نہیں ہے - یہ صارف کے تجربے، رسائی، اور یہاں تک کہ ویب سائٹ کی کارکردگی کے لیے بھی اہم ہے۔

[JavaScript کارڈ ظاہر کرنے کے لیے کلک کریں]
آخر میں، JavaScript - پروگرامنگ زبان جو انٹرایکٹویٹی، متحرک رفتار، اور کلائنٹ سائیڈ لاجک شامل کرتا ہے۔

JavaScript ایک سادہ اسکرپٹنگ زبان سے ایک طاقتور پروگرامنگ زبان میں تبدیل ہو گیا ہے۔ جدید JavaScript (ES6+) میں arrow functions، destructuring، async/await، اور modules جیسے فیچرز شامل ہیں۔ یہ وہ چیز ہے جو ویب سائٹس کو انٹرایکٹو اور صارف کے اعمال کے لیے جوابدہ بناتا ہے۔

یہاں کلیدی بات یہ ہے: یہ تینوں ٹیکنالوجیز مل کر کام کرتی ہیں، لیکن ان کی الگ ذمہ داریاں ہیں - ہم اسے 'concerns کی علیحدگی' کہتے ہیں۔ HTML ڈھانچے کے لیے، CSS پیشکش کے لیے، JavaScript رفتار کے لیے۔"
                        </div>
                    </div>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interactive Demo Idea</h5>
                <p>Show a website and ask students to identify what each technology contributes. Point to text (HTML), colors/layout (CSS), and interactive elements (JavaScript).</p>
            </div>
        </div>

        <!-- Slide 5: HTML Example -->
        <div id="slide-5" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">5</div>
                <h3 class="slide-title">HTML Example - Structure in Action</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 6-8 minutes | <i class="fas fa-eye"></i> Purpose: Practical HTML Understanding
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> HTML Code Explanation Script / HTML کوڈ کی وضاحتی اسکرپٹ</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"Let's look at a real HTML example and break it down line by line.

[Point to the code on screen]

First line: &lt;!DOCTYPE html&gt; - This tells the browser we're using HTML5. It's essential for modern web standards.

Next: &lt;html&gt; - This is our root element, containing everything else.

The &lt;head&gt; section contains metadata - information ABOUT the page, not content that users see:
- &lt;title&gt; sets what appears in the browser tab
- This is also where we'd link CSS files and add meta tags

The &lt;body&gt; section contains the actual content users see:
- &lt;h1&gt; is our main heading - there should only be one per page for SEO
- &lt;p&gt; is a paragraph element

Notice the proper nesting and indentation - this makes code readable and maintainable.

This might look simple, but this structure is the foundation of every website you've ever visited - from Google to Facebook to Amazon. They all start with this basic HTML structure.

The beauty of HTML is its simplicity and semantic meaning. Screen readers, search engines, and other tools can understand the structure and purpose of our content because we use meaningful HTML elements."
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"آئیے ایک حقیقی HTML مثال کو دیکھتے ہیں اور اسے لائن بائی لائن توڑتے ہیں۔

[اسکرین پر کوڈ کی طرف اشارہ کریں]

پہلی لائن: &lt;!DOCTYPE html&gt; - یہ براؤزر کو بتاتا ہے کہ ہم HTML5 استعمال کر رہے ہیں۔ یہ جدید ویب معیارات کے لیے ضروری ہے۔

اگلا: &lt;html&gt; - یہ ہمارا بنیادی عنصر ہے، جس میں باقی سب کچھ شامل ہے۔

&lt;head&gt; سیکشن میں میٹا ڈیٹا ہوتا ہے - صفحے کے بارے میں معلومات، نہ کہ وہ مواد جو صارف دیکھتے ہیں:
- &lt;title&gt; وہ سیٹ کرتا ہے جو براؤزر ٹیب میں نظر آتا ہے
- یہ وہ جگہ بھی ہے جہاں ہم CSS فائلیں لنک کریں گے اور میٹا ٹیگز شامل کریں گے

&lt;body&gt; سیکشن میں وہ اصل مواد ہوتا ہے جو صارف دیکھتے ہیں:
- &lt;h1&gt; ہماری اصل سرخی ہے - SEO کے لیے ہر صفحے پر صرف ایک ہونی چاہیے
- &lt;p&gt; ایک پیراگراف عنصر ہے

مناسب nesting اور indentation کو نوٹ کریں - یہ کوڈ کو پڑھنے اور برقرار رکھنے کے قابل بناتا ہے۔

یہ سادہ لگ سکتا ہے، لیکن یہ ڈھانچہ ہر اس ویب سائٹ کی بنیاد ہے جس پر آپ نے کبھی جانا ہے - گوگل سے لے کر فیس بک سے ایمیزون تک۔ وہ سب اسی بنیادی HTML ڈھانچے سے شروع ہوتے ہیں۔

HTML کی خوبصورتی اس کی سادگی اور معنی خیز معنی میں ہے۔ اسکرین ریڈرز، سرچ انجنز، اور دیگر ٹولز ہمارے مواد کی ساخت اور مقصد کو سمجھ سکتے ہیں کیونکہ ہم معنی خیز HTML عناصر استعمال کرتے ہیں۔"
                        </div>
                    </div>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interactive Teaching Moment</h5>
                <p>Ask students: "What would happen if we removed the DOCTYPE?" or "Why do you think we separate head and body?" This encourages critical thinking.</p>
            </div>
        </div>

        <!-- Slide 6: CSS Example -->
        <div id="slide-6" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">6</div>
                <h3 class="slide-title">CSS Example - Styling and Layout</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 8-10 minutes | <i class="fas fa-eye"></i> Purpose: CSS Practical Application
            </div>

            <div class="content-grid">
                <div class="content-section">
                    <h4><i class="fas fa-palette"></i> CSS Concepts Covered</h4>
                    <ul class="key-points">
                        <li>Selectors and specificity</li>
                        <li>Box model (margin, padding)</li>
                        <li>Typography and color</li>
                        <li>Layout techniques</li>
                    </ul>
                </div>

                <div class="content-section">
                    <h4><i class="fas fa-mobile-alt"></i> Responsive Design</h4>
                    <ul class="key-points">
                        <li>Max-width for responsive containers</li>
                        <li>Margin auto for centering</li>
                        <li>Relative units (em, rem, %)</li>
                        <li>Mobile-first approach</li>
                    </ul>
                </div>
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> CSS Explanation Script / CSS وضاحتی اسکرپٹ</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"Now let's look at CSS - Cascading Style Sheets. This is where we make our HTML look beautiful and professional.

[Point to the CSS code]

Look at this first rule: h1 { ... } - This targets all h1 elements on the page. We're setting the color to a dark gray (#333), making the font size 2em (twice the default size), and centering the text.

The second rule targets elements with the class 'container'. The dot before 'container' indicates it's a class selector. Here we're:
- Setting max-width to 1200px for responsive design
- Using 'margin: 0 auto' to center the container
- Adding 20px padding for breathing room

This demonstrates the power of CSS - we can style multiple elements at once, create responsive layouts, and separate our styling from our content structure.

Notice how CSS follows a pattern: selector { property: value; }. This consistency makes it easy to learn and maintain."
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"اب آئیے CSS - کیسکیڈنگ اسٹائل شیٹس کو دیکھتے ہیں۔ یہ وہ جگہ ہے جہاں ہم اپنے HTML کو خوبصورت اور پیشہ ورانہ بناتے ہیں۔

[CSS کوڈ کی طرف اشارہ کریں]

اس پہلے قانون کو دیکھیں: h1 { ... } - یہ صفحے پر تمام h1 عناصر کو نشانہ بناتا ہے۔ ہم رنگ کو گہرے سرمئی (#333) پر سیٹ کر رہے ہیں، فونٹ سائز کو 2em (ڈیفالٹ سائز سے دوگنا) بنا رہے ہیں، اور ٹیکسٹ کو وسط میں لا رہے ہیں۔

دوسرا قانون 'container' کلاس والے عناصر کو نشانہ بناتا ہے۔ 'container' سے پہلے نقطہ اس بات کو ظاہر کرتا ہے کہ یہ ایک کلاس سلیکٹر ہے۔ یہاں ہم:
- ریسپانسو ڈیزائن کے لیے max-width کو 1200px سیٹ کر رہے ہیں
- کنٹینر کو وسط میں لانے کے لیے 'margin: 0 auto' استعمال کر رہے ہیں
- سانس لینے کی جگہ کے لیے 20px padding شامل کر رہے ہیں

یہ CSS کی طاقت کو ظاہر کرتا ہے - ہم ایک ساتھ متعدد عناصر کو اسٹائل کر سکتے ہیں، ریسپانسو لے آؤٹس بنا سکتے ہیں، اور اپنی اسٹائلنگ کو اپنے مواد کی ساخت سے الگ کر سکتے ہیں۔

نوٹ کریں کہ CSS ایک پیٹرن کی پیروی کرتا ہے: selector { property: value; }۔ یہ مستقل مزاجی اسے سیکھنے اور برقرار رکھنے میں آسان بناتا ہے۔"
                        </div>
                    </div>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Demonstration Idea / مظاہرہ کا خیال</h5>
                <p><strong>English:</strong> Show the same HTML with and without CSS. Then apply styles one by one to show the transformation. Use browser dev tools to toggle styles on/off.</p>
                <p><strong>اردو:</strong> وہی HTML CSS کے ساتھ اور اس کے بغیر دکھائیں۔ پھر تبدیلی دکھانے کے لیے ایک ایک کرکے اسٹائلز لگائیں۔ اسٹائلز کو آن/آف کرنے کے لیے براؤزر ڈیو ٹولز استعمال کریں۔</p>
            </div>

            <div class="warning-box">
                <h5><i class="fas fa-exclamation-triangle"></i> Common CSS Pitfalls / عام CSS مسائل</h5>
                <p><strong>English:</strong> Discuss CSS specificity issues, the importance of consistent naming conventions, and why inline styles should be avoided in production code.</p>
                <p><strong>اردو:</strong> CSS specificity کے مسائل، مستقل نام کے طریقوں کی اہمیت، اور پروڈکشن کوڈ میں inline styles سے کیوں بچنا چاہیے، اس پر بحث کریں۔</p>
            </div>
        </div>

        <!-- Slide 7: JavaScript Example -->
        <div id="slide-7" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">7</div>
                <h3 class="slide-title">JavaScript Example - Adding Interactivity</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 10-12 minutes | <i class="fas fa-eye"></i> Purpose: JavaScript Fundamentals
            </div>

            <div class="content-grid">
                <div class="content-section">
                    <h4><i class="fas fa-cogs"></i> JavaScript Concepts</h4>
                    <ul class="key-points">
                        <li>DOM manipulation methods</li>
                        <li>Event handling</li>
                        <li>Functions and scope</li>
                        <li>Modern ES6+ syntax</li>
                    </ul>
                </div>

                <div class="content-section">
                    <h4><i class="fas fa-rocket"></i> Advanced Topics</h4>
                    <ul class="key-points">
                        <li>Event delegation</li>
                        <li>Async programming</li>
                        <li>Error handling</li>
                        <li>Performance considerations</li>
                    </ul>
                </div>
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> JavaScript Explanation Script / JavaScript وضاحتی اسکرپٹ</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"Now we come to JavaScript - the programming language that brings our websites to life!

[Point to the JavaScript code]

Let's break down this code:

First, we have a function called 'greetUser'. This function:
1. Gets the value from an input field with ID 'name'
2. Shows an alert with a personalized greeting
3. Demonstrates how JavaScript can interact with HTML elements

The second part shows an event listener. 'DOMContentLoaded' is a special event that fires when the HTML document has been completely loaded and parsed. This is better than the old 'window.onload' because it doesn't wait for images and other resources.

This is the power of JavaScript - it can:
- Respond to user actions (clicks, typing, etc.)
- Modify the content of the page dynamically
- Validate forms before submission
- Create interactive experiences

Modern JavaScript (ES6+) has made the language much more powerful and easier to work with."
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"اب ہم JavaScript پر آتے ہیں - وہ پروگرامنگ زبان جو ہماری ویب سائٹس کو زندہ کرتا ہے!

[JavaScript کوڈ کی طرف اشارہ کریں]

آئیے اس کوڈ کو توڑتے ہیں:

پہلے، ہمارے پاس 'greetUser' نام کا ایک فنکشن ہے۔ یہ فنکشن:
1. 'name' ID والے input field سے value حاصل کرتا ہے
2. ذاتی سلام کے ساتھ ایک alert دکھاتا ہے
3. یہ ظاہر کرتا ہے کہ JavaScript HTML عناصر کے ساتھ کیسے تعامل کر سکتا ہے

دوسرا حصہ ایک event listener دکھاتا ہے۔ 'DOMContentLoaded' ایک خاص event ہے جو اس وقت چلتا ہے جب HTML document مکمل طور پر لوڈ اور parse ہو چکا ہو۔ یہ پرانے 'window.onload' سے بہتر ہے کیونکہ یہ تصاویر اور دیگر وسائل کا انتظار نہیں کرتا۔

یہ JavaScript کی طاقت ہے - یہ کر سکتا ہے:
- صارف کے اعمال کا جواب دینا (کلکس، ٹائپنگ، وغیرہ)
- صفحے کے مواد کو متحرک طریقے سے تبدیل کرنا
- جمع کرنے سے پہلے فارمز کی تصدیق کرنا
- انٹرایکٹو تجربات بنانا

جدید JavaScript (ES6+) نے زبان کو بہت زیادہ طاقتور اور کام کرنے میں آسان بنا دیا ہے۔"
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-section">
                <h4><i class="fas fa-code"></i> Code Explanation / کوڈ کی وضاحت</h4>
                <div class="code-example">
// Function Declaration vs Expression
function greetUser() { ... }  // Hoisted
const greetUser = () => { ... }  // Not hoisted, modern syntax

// DOM Ready Event
document.addEventListener('DOMContentLoaded', function() {
    // Code runs after DOM is fully loaded
    // Better than window.onload for performance
});
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Interactive Demo / انٹرایکٹو مظاہرہ</h5>
                <p><strong>English:</strong> Create a simple HTML page with an input field and button. Show how JavaScript can read the input, process it, and update the page dynamically.</p>
                <p><strong>اردو:</strong> ایک input field اور button کے ساتھ ایک سادہ HTML صفحہ بنائیں۔ دکھائیں کہ JavaScript کیسے input پڑھ سکتا ہے، اسے process کر سکتا ہے، اور صفحے کو متحرک طریقے سے اپ ڈیٹ کر سکتا ہے۔</p>
            </div>
        </div>

        <!-- Slide 8: Backend Technologies -->
        <div id="slide-8" class="slide-section">
            <div class="slide-header">
                <div class="slide-number">8</div>
                <h3 class="slide-title">Backend Technologies - Server-Side Power</h3>
            </div>

            <div class="timing-info">
                <i class="fas fa-clock"></i> Duration: 12-15 minutes | <i class="fas fa-eye"></i> Purpose: Backend Ecosystem Overview
            </div>

            <div class="content-grid">
                <div class="content-section">
                    <h4><i class="fas fa-server"></i> Server-Side Languages</h4>
                    <ul class="key-points">
                        <li>Node.js: JavaScript everywhere</li>
                        <li>Python: Django, Flask, FastAPI</li>
                        <li>PHP: WordPress, Laravel</li>
                        <li>Java: Enterprise applications</li>
                    </ul>
                </div>

                <div class="content-section">
                    <h4><i class="fas fa-database"></i> Database Technologies</h4>
                    <ul class="key-points">
                        <li>SQL vs NoSQL differences</li>
                        <li>PostgreSQL: Advanced features</li>
                        <li>MongoDB: Document-based</li>
                        <li>Redis: Caching and sessions</li>
                    </ul>
                </div>
            </div>

            <div class="content-section">
                <h4><i class="fas fa-microphone"></i> Backend Technologies Script / بیک اینڈ ٹیکنالوجیز اسکرپٹ</h4>

                <div class="bilingual-script">
                    <div class="script-section english-script">
                        <div class="script-header">🇬🇧 English Script</div>
                        <div class="english-text">
"Now let's move to the backend - the server-side technologies that power web applications behind the scenes.

[CLICK to reveal Languages section]
First, let's talk about backend programming languages:
- Node.js allows us to use JavaScript on the server - same language, different environment
- Python is incredibly popular for its simplicity and powerful frameworks like Django and FastAPI
- PHP powers a huge portion of the web, including WordPress
- Java is the enterprise standard for large-scale applications

[CLICK to reveal Frameworks section]
Frameworks make development faster and more organized:
- Express.js for Node.js - minimal and flexible
- Django for Python - 'batteries included' framework
- Laravel for PHP - elegant and feature-rich
- Spring Boot for Java - enterprise-grade applications

[CLICK to reveal Databases section]
Data storage is crucial:
- PostgreSQL and MySQL for structured, relational data
- MongoDB for flexible, document-based storage
- Redis for caching and session management

[CLICK to reveal APIs section]
APIs connect frontend and backend:
- REST is the most common standard
- GraphQL offers more flexibility for complex applications

The key is choosing the right tool for your specific project needs."
                        </div>
                    </div>

                    <div class="script-section urdu-script">
                        <div class="script-header">🇵🇰 اردو اسکرپٹ</div>
                        <div class="urdu-text">
"اب آئیے بیک اینڈ کی طرف چلتے ہیں - سرور سائیڈ ٹیکنالوجیز جو پردے کے پیچھے ویب ایپلیکیشنز کو طاقت فراہم کرتی ہیں۔

[Languages سیکشن ظاہر کرنے کے لیے کلک کریں]
پہلے، آئیے بیک اینڈ پروگرامنگ زبانوں کے بارے میں بات کرتے ہیں:
- Node.js ہمیں سرور پر JavaScript استعمال کرنے کی اجازت دیتا ہے - وہی زبان، مختلف ماحول
- Python اپنی سادگی اور طاقتور فریم ورکس جیسے Django اور FastAPI کے لیے انتہائی مقبول ہے
- PHP ویب کا ایک بہت بڑا حصہ چلاتا ہے، بشمول WordPress
- Java بڑے پیمانے کی ایپلیکیشنز کے لیے انٹرپرائز معیار ہے

[Frameworks سیکشن ظاہر کرنے کے لیے کلک کریں]
فریم ورکس ڈیولپمنٹ کو تیز اور زیادہ منظم بناتے ہیں:
- Node.js کے لیے Express.js - کم سے کم اور لچکدار
- Python کے لیے Django - 'بیٹریز شامل' فریم ورک
- PHP کے لیے Laravel - خوبصورت اور فیچر سے بھرپور
- Java کے لیے Spring Boot - انٹرپرائز درجے کی ایپلیکیشنز

[Databases سیکشن ظاہر کرنے کے لیے کلک کریں]
ڈیٹا اسٹوریج اہم ہے:
- PostgreSQL اور MySQL منظم، رشتہ دار ڈیٹا کے لیے
- MongoDB لچکدار، دستاویز پر مبنی اسٹوریج کے لیے
- Redis کیشنگ اور سیشن منیجمنٹ کے لیے

[APIs سیکشن ظاہر کرنے کے لیے کلک کریں]
APIs فرنٹ اینڈ اور بیک اینڈ کو جوڑتے ہیں:
- REST سب سے عام معیار ہے
- GraphQL پیچیدہ ایپلیکیشنز کے لیے زیادہ لچک فراہم کرتا ہے

کلیدی بات یہ ہے کہ آپ کے مخصوص پروجیکٹ کی ضروریات کے لیے صحیح ٹول کا انتخاب کرنا۔"
                        </div>
                    </div>
                </div>
            </div>

            <div class="tips-box">
                <h5><i class="fas fa-lightbulb"></i> Career Guidance / کیریئر رہنمائی</h5>
                <p><strong>English:</strong> Discuss job market trends. Node.js and Python are currently in high demand. Mention that learning one backend language well is better than knowing many superficially.</p>
                <p><strong>اردو:</strong> جاب مارکیٹ کے رجحانات پر بحث کریں۔ Node.js اور Python فی الوقت بہت زیادہ مانگ میں ہیں۔ یہ بتائیں کہ ایک بیک اینڈ زبان اچھی طرح سیکھنا کئی کو سطحی طور پر جاننے سے بہتر ہے۔</p>
            </div>

            <div class="content-section">
                <h4><i class="fas fa-plug"></i> API Development / API ڈیولپمنٹ</h4>
                <div class="bilingual-content">
                    <div class="english-text">
                        <ul class="key-points">
                            <li>REST API principles</li>
                            <li>GraphQL advantages</li>
                            <li>Authentication methods</li>
                            <li>API documentation importance</li>
                        </ul>
                    </div>
                    <div class="urdu-text">
                        <ul class="key-points">
                            <li>REST API کے اصول</li>
                            <li>GraphQL کے فوائد</li>
                            <li>تصدیق کے طریقے</li>
                            <li>API دستاویزات کی اہمیت</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide-section" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white;">
            <div style="text-align: center; padding: 2rem;">
                <h2><i class="fas fa-graduation-cap"></i> Instructor Guide Complete</h2>
                <p style="font-size: 1.2rem; margin: 1rem 0;">This guide covers the first 8 slides of your presentation.</p>
                <p>For additional slides, continue with the same detailed approach, focusing on:</p>
                <ul style="list-style: none; padding: 0; margin: 2rem 0;">
                    <li><i class="fas fa-check"></i> Clear learning objectives</li>
                    <li><i class="fas fa-check"></i> Interactive elements</li>
                    <li><i class="fas fa-check"></i> Real-world examples</li>
                    <li><i class="fas fa-check"></i> Common pitfalls to address</li>
                </ul>
                <p><strong>Remember:</strong> Adapt timing based on student engagement and questions!</p>
            </div>
        </div>

    <script>
        function showLanguage(lang) {
            // Remove active class from all buttons
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            event.target.classList.add('active');

            const englishScripts = document.querySelectorAll('.english-script');
            const urduScripts = document.querySelectorAll('.urdu-script');
            const bilingualScripts = document.querySelectorAll('.bilingual-script');

            if (lang === 'english') {
                englishScripts.forEach(el => el.style.display = 'block');
                urduScripts.forEach(el => el.style.display = 'none');
                bilingualScripts.forEach(el => el.style.gridTemplateColumns = '1fr');
            } else if (lang === 'urdu') {
                englishScripts.forEach(el => el.style.display = 'none');
                urduScripts.forEach(el => el.style.display = 'block');
                bilingualScripts.forEach(el => el.style.gridTemplateColumns = '1fr');
            } else {
                englishScripts.forEach(el => el.style.display = 'block');
                urduScripts.forEach(el => el.style.display = 'block');
                bilingualScripts.forEach(el => el.style.gridTemplateColumns = '1fr 1fr');
            }
        }

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
