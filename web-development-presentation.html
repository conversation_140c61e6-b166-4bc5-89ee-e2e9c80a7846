<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/black.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <title>Web Development Fundamentals</title>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
            --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
            --accent-gradient: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
            --primary-color: #6366f1;
            --secondary-color: #06b6d4;
            --accent-color: #f59e0b;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #94a3b8;
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-card: rgba(30, 41, 59, 0.8);
            --border-color: rgba(99, 102, 241, 0.2);
            --shadow-primary: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-glow: 0 0 40px rgba(99, 102, 241, 0.3);
        }

        .reveal {
            font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--bg-primary);
            position: relative;
            overflow: hidden;
        }

        /* Modern animated background */
        .reveal::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.06) 0%, transparent 50%);
            pointer-events: none;
            z-index: -2;
            animation: backgroundFlow 25s ease-in-out infinite;
        }

        .reveal::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(45deg, transparent 30%, rgba(99, 102, 241, 0.02) 50%, transparent 70%),
                linear-gradient(-45deg, transparent 30%, rgba(6, 182, 212, 0.02) 50%, transparent 70%);
            pointer-events: none;
            z-index: -1;
            animation: meshMove 30s linear infinite;
        }

        @keyframes backgroundFlow {
            0%, 100% {
                background:
                    radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.12) 0%, transparent 60%),
                    radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 0%, transparent 60%),
                    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.08) 0%, transparent 60%);
            }
            33% {
                background:
                    radial-gradient(circle at 75% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 60%),
                    radial-gradient(circle at 25% 75%, rgba(6, 182, 212, 0.12) 0%, transparent 60%),
                    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.06) 0%, transparent 60%);
            }
            66% {
                background:
                    radial-gradient(circle at 75% 75%, rgba(99, 102, 241, 0.08) 0%, transparent 60%),
                    radial-gradient(circle at 25% 25%, rgba(6, 182, 212, 0.1) 0%, transparent 60%),
                    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.12) 0%, transparent 60%);
            }
        }

        @keyframes meshMove {
            0% { transform: translateX(-50px) translateY(-50px); }
            100% { transform: translateX(50px) translateY(50px); }
        }
        
        .reveal h1, .reveal h2 {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            font-weight: 700;
            letter-spacing: -0.025em;
            position: relative;
            margin-bottom: 2rem;
        }

        .reveal h1 {
            font-size: 3.5rem;
            line-height: 1.1;
        }

        .reveal h2 {
            font-size: 2.5rem;
            line-height: 1.2;
        }

        .reveal h1::after, .reveal h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--secondary-gradient);
            border-radius: 2px;
            box-shadow: var(--shadow-glow);
        }

        .reveal h3 {
            color: var(--text-primary) !important;
            font-size: 1.75rem !important;
            font-weight: 600 !important;
            margin-bottom: 1.5rem !important;
            text-shadow: none !important;
            position: relative;
        }

        .reveal h3::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 100%;
            background: var(--secondary-gradient);
            border-radius: 2px;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
            perspective: 1200px;
        }

        .tech-card {
            background: var(--bg-card);
            backdrop-filter: blur(24px);
            border: 1px solid var(--border-color);
            padding: 2.5rem 2rem;
            border-radius: 24px;
            text-align: center;
            transform: translateY(10px);
            transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow: var(--shadow-primary);
            position: relative;
            overflow: hidden;
        }

        .tech-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.5s ease;
        }

        .tech-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .tech-card:hover::before {
            transform: scaleX(1);
        }

        .tech-card:hover::after {
            left: 100%;
        }

        .tech-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow:
                var(--shadow-primary),
                var(--shadow-glow);
            border-color: var(--primary-color);
        }
        
        .tech-card i {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 0 20px rgba(99, 102, 241, 0.4));
            transition: all 0.3s ease;
        }

        .tech-card:hover i {
            transform: scale(1.1) rotateY(10deg);
            filter: drop-shadow(0 0 30px rgba(99, 102, 241, 0.6));
        }

        .tech-card h3 {
            color: var(--text-primary) !important;
            font-size: 1.5rem !important;
            margin-bottom: 1rem !important;
            text-shadow: none !important;
            font-weight: 600 !important;
        }

        .tech-card p {
            color: var(--text-secondary);
            font-weight: 400;
            font-size: 1rem;
            line-height: 1.5;
        }
        
        .code-example {
            background: linear-gradient(145deg, var(--bg-secondary), var(--bg-primary));
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-primary);
            position: relative;
            overflow: hidden;
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
        }

        .code-example::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--secondary-gradient);
        }

        .code-example::after {
            content: '';
            position: absolute;
            top: 1rem;
            left: 1rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            box-shadow:
                20px 0 0 #f59e0b,
                40px 0 0 #10b981;
        }

        .code-example pre {
            margin-top: 2rem;
        }

        .code-example code {
            font-family: inherit;
            font-size: 0.9rem;
            line-height: 1.6;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(6, 182, 212, 0.08) 100%);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 2.5rem;
            margin: 2rem 0;
            position: relative;
            backdrop-filter: blur(16px);
            overflow: hidden;
        }

        .highlight-box::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: var(--secondary-gradient);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
        }

        .highlight-box::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            animation: rotate 20s linear infinite;
            z-index: -1;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .career-path {
            background: var(--primary-gradient);
            color: white;
            padding: 1.25rem 2rem;
            border-radius: 60px;
            margin: 1rem;
            display: inline-block;
            transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
            box-shadow: var(--shadow-primary);
            position: relative;
            overflow: hidden;
            font-weight: 500;
            font-size: 1.1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .career-path::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.25), transparent);
            transition: left 0.6s ease;
        }

        .career-path::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: inherit;
            padding: 2px;
            background: var(--accent-gradient);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .career-path:hover::before {
            left: 100%;
        }

        .career-path:hover::after {
            opacity: 1;
        }

        .career-path:hover {
            transform: translateY(-12px) scale(1.08);
            box-shadow:
                var(--shadow-primary),
                var(--shadow-glow);
        }
        
        .animated-title {
            animation: titleAnimation 2.5s cubic-bezier(0.23, 1, 0.32, 1);
            font-size: 4rem !important;
            text-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
            margin-bottom: 2rem !important;
            position: relative;
        }

        .animated-title::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 6px;
            background: var(--accent-gradient);
            border-radius: 3px;
            animation: titleUnderline 2.5s cubic-bezier(0.23, 1, 0.32, 1) 0.5s both;
        }

        @keyframes titleAnimation {
            0% {
                opacity: 0;
                transform: translateY(60px) scale(0.8);
                filter: blur(15px);
            }
            60% {
                transform: translateY(-15px) scale(1.05);
                filter: blur(0);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        @keyframes titleUnderline {
            0% { width: 0; opacity: 0; }
            100% { width: 120px; opacity: 1; }
        }

        /* Enhanced floating animation for icons */
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(1);
            }
            25% {
                transform: translateY(-15px) rotate(3deg) scale(1.05);
            }
            50% {
                transform: translateY(-5px) rotate(-2deg) scale(1.02);
            }
            75% {
                transform: translateY(-10px) rotate(1deg) scale(1.03);
            }
        }

        .floating-icon {
            animation: float 4s ease-in-out infinite;
            transition: all 0.3s ease;
        }

        .floating-icon:hover {
            animation-play-state: paused;
            transform: scale(1.2) !important;
            filter: drop-shadow(0 0 20px currentColor);
        }

        .floating-icon:nth-child(2) {
            animation-delay: 0.8s;
        }

        .floating-icon:nth-child(3) {
            animation-delay: 1.6s;
        }
        
        .slide-number {
            color: var(--primary-color) !important;
            font-weight: 600 !important;
            font-family: 'JetBrains Mono', monospace !important;
        }

        .progress {
            color: var(--primary-color) !important;
            height: 6px !important;
            background: var(--primary-gradient) !important;
        }

        .reveal .slides section .fragment.highlight-current-blue.current-fragment {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            transform: scale(1.05);
            box-shadow: var(--shadow-glow);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Enhanced list styling */
        .reveal ul li {
            margin-bottom: 1.25rem;
            position: relative;
            padding-left: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
            color: var(--text-secondary);
        }

        .reveal ul li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.7rem;
            width: 8px;
            height: 8px;
            background: var(--secondary-gradient);
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
        }

        .reveal ol li {
            margin-bottom: 1.25rem;
            font-size: 1.1rem;
            line-height: 1.6;
            color: var(--text-secondary);
        }

        /* Enhanced glowing effect */
        .glow {
            box-shadow: var(--shadow-glow);
            animation: pulse-glow 3s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from {
                box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
                filter: brightness(1);
            }
            to {
                box-shadow: 0 0 50px rgba(6, 182, 212, 0.8);
                filter: brightness(1.1);
            }
        }

        /* Modern section backgrounds */
        .reveal .slides section[data-background-gradient] {
            position: relative;
            overflow: hidden;
        }

        .reveal .slides section[data-background-gradient]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.08) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.08) 0%, transparent 50%);
            pointer-events: none;
            animation: sectionGlow 15s ease-in-out infinite;
        }

        @keyframes sectionGlow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 0.8; }
        }

        /* Improved text styles */
        .reveal p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .reveal strong {
            color: var(--text-primary);
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- Title Slide -->
            <section data-background-gradient="linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)">
                <h1 class="animated-title">Web Development Fundamentals</h1>
                <h3 data-fragment-index="1" class="fragment fade-in" style="color: var(--text-primary); font-size: 2rem; margin-bottom: 2rem;">
                    Building Modern Web Applications
                </h3>
                <p data-fragment-index="2" class="fragment fade-in" style="font-size: 1.3rem; color: var(--text-secondary); margin-bottom: 3rem;">
                    <i class="fas fa-graduation-cap" style="margin-right: 0.5rem; color: var(--accent-color);"></i>
                    A comprehensive guide to modern web development
                </p>
                <div data-fragment-index="3" class="fragment fade-in" style="margin-top: 4rem;">
                    <i class="fab fa-html5 floating-icon" style="font-size: 4rem; margin: 0 2rem; color: #e34c26;"></i>
                    <i class="fab fa-css3-alt floating-icon" style="font-size: 4rem; margin: 0 2rem; color: #1572b6;"></i>
                    <i class="fab fa-js-square floating-icon" style="font-size: 4rem; margin: 0 2rem; color: #f7df1e;"></i>
                </div>
                <p data-fragment-index="4" class="fragment fade-in" style="margin-top: 3rem; font-size: 1rem; color: var(--text-muted);">
                    Press <kbd style="background: rgba(255,255,255,0.1); padding: 0.25rem 0.5rem; border-radius: 4px;">Space</kbd> to navigate
                </p>
            </section>

            <!-- What is Web Development -->
            <section data-transition="zoom">
                <h2><i class="fas fa-globe" style="margin-right: 1rem; color: var(--secondary-color);"></i>What is Web Development?</h2>
                <div class="highlight-box">
                    <p style="font-size: 1.3rem; margin-bottom: 2rem; color: var(--text-primary);">
                        Web development is the process of building and maintaining websites and web applications that run on the internet.
                    </p>
                    <ul>
                        <li class="fragment highlight-current-blue">
                            <strong>Frontend Development:</strong> Creating user interfaces and experiences (client-side)
                        </li>
                        <li class="fragment highlight-current-blue">
                            <strong>Backend Development:</strong> Building server logic, databases, and APIs (server-side)
                        </li>
                        <li class="fragment highlight-current-blue">
                            <strong>Full-Stack Development:</strong> Combining both frontend and backend skills
                        </li>
                    </ul>
                </div>
            </section>

            <!-- Frontend Technologies -->
            <section data-transition="slide">
                <h2><i class="fas fa-palette" style="margin-right: 1rem; color: var(--secondary-color);"></i>Frontend Technologies</h2>
                <p style="font-size: 1.2rem; margin-bottom: 3rem; color: var(--text-secondary);">
                    The building blocks of user interfaces and web experiences
                </p>
                <div class="tech-grid">
                    <div class="tech-card fragment zoom-in" data-fragment-index="1">
                        <i class="fab fa-html5" style="color: #e34c26;"></i>
                        <h3>HTML</h3>
                        <p>HyperText Markup Language - provides the structure and semantic meaning to web content</p>
                    </div>
                    <div class="tech-card fragment zoom-in" data-fragment-index="2">
                        <i class="fab fa-css3-alt" style="color: #1572b6;"></i>
                        <h3>CSS</h3>
                        <p>Cascading Style Sheets - controls the visual presentation, layout, and responsive design</p>
                    </div>
                    <div class="tech-card fragment zoom-in" data-fragment-index="3">
                        <i class="fab fa-js-square" style="color: #f7df1e;"></i>
                        <h3>JavaScript</h3>
                        <p>Programming language that adds interactivity, dynamic behavior, and client-side logic</p>
                    </div>
                </div>
            </section>

            <!-- HTML Example -->
            <section data-transition="convex">
                <h2><i class="fab fa-html5"></i> HTML - Structure</h2>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim data-line-numbers="1-10|3-5|6-9">
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;My Website&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;Welcome&lt;/h1&gt;
    &lt;p&gt;This is my first webpage&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;
                    </code></pre>
                </div>
            </section>

            <!-- CSS Example -->
            <section data-transition="convex">
                <h2><i class="fab fa-css3-alt"></i> CSS - Styling</h2>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim data-line-numbers="1-5|7-11">
h1 {
    color: #333;
    font-size: 2em;
    text-align: center;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}
                    </code></pre>
                </div>
            </section>

            <!-- JavaScript Example -->
            <section data-transition="convex">
                <h2><i class="fab fa-js-square"></i> JavaScript - Interactivity</h2>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim data-line-numbers="1-4|6-8">
function greetUser() {
    const name = document.getElementById('name').value;
    alert('Hello, ' + name + '!');
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded!');
});
                    </code></pre>
                </div>
            </section>

            <!-- Backend Technologies -->
            <section data-background-gradient="linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)">
                <h2><i class="fas fa-server" style="margin-right: 1rem; color: var(--accent-color);"></i>Backend Technologies</h2>
                <p style="font-size: 1.2rem; margin-bottom: 3rem; color: var(--text-secondary);">
                    Server-side technologies that power web applications
                </p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                    <div class="fragment fade-right" style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 16px; backdrop-filter: blur(10px);">
                        <h4 style="color: var(--accent-color); font-size: 1.5rem; margin-bottom: 1rem;">
                            <i class="fas fa-code" style="margin-right: 0.5rem;"></i> Languages
                        </h4>
                        <p style="font-size: 1.1rem; line-height: 1.6;">Node.js, Python, PHP, Java, C#, Go, Ruby</p>
                    </div>
                    <div class="fragment fade-left" style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 16px; backdrop-filter: blur(10px);">
                        <h4 style="color: var(--accent-color); font-size: 1.5rem; margin-bottom: 1rem;">
                            <i class="fas fa-layer-group" style="margin-right: 0.5rem;"></i> Frameworks
                        </h4>
                        <p style="font-size: 1.1rem; line-height: 1.6;">Express, Django, Laravel, Spring Boot, FastAPI</p>
                    </div>
                    <div class="fragment fade-right" style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 16px; backdrop-filter: blur(10px);">
                        <h4 style="color: var(--accent-color); font-size: 1.5rem; margin-bottom: 1rem;">
                            <i class="fas fa-database" style="margin-right: 0.5rem;"></i> Databases
                        </h4>
                        <p style="font-size: 1.1rem; line-height: 1.6;">PostgreSQL, MySQL, MongoDB, Redis, SQLite</p>
                    </div>
                    <div class="fragment fade-left" style="background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 16px; backdrop-filter: blur(10px);">
                        <h4 style="color: var(--accent-color); font-size: 1.5rem; margin-bottom: 1rem;">
                            <i class="fas fa-plug" style="margin-right: 0.5rem;"></i> APIs
                        </h4>
                        <p style="font-size: 1.1rem; line-height: 1.6;">REST, GraphQL, WebSockets, gRPC</p>
                    </div>
                </div>
            </section>

            <!-- Development Tools -->
            <section>
                <h2><i class="fas fa-tools" style="margin-right: 1rem; color: var(--secondary-color);"></i>Essential Development Tools</h2>
                <p style="font-size: 1.2rem; margin-bottom: 3rem; color: var(--text-secondary);">
                    Tools that enhance productivity and streamline the development process
                </p>
                <div class="highlight-box">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-code" style="margin-right: 0.5rem;"></i>Code Editors
                            </h4>
                            <p>VS Code, WebStorm, Sublime Text, Vim</p>
                        </div>
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fab fa-git-alt" style="margin-right: 0.5rem;"></i>Version Control
                            </h4>
                            <p>Git, GitHub, GitLab, Bitbucket</p>
                        </div>
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-box" style="margin-right: 0.5rem;"></i>Package Managers
                            </h4>
                            <p>npm, yarn, pnpm, pip, composer</p>
                        </div>
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-cogs" style="margin-right: 0.5rem;"></i>Build Tools
                            </h4>
                            <p>Vite, Webpack, Parcel, Rollup</p>
                        </div>
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-vial" style="margin-right: 0.5rem;"></i>Testing
                            </h4>
                            <p>Jest, Vitest, Cypress, Playwright</p>
                        </div>
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-cloud" style="margin-right: 0.5rem;"></i>Deployment
                            </h4>
                            <p>Vercel, Netlify, AWS, Docker</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Modern Frameworks -->
            <section data-transition="zoom">
                <h2><i class="fas fa-rocket" style="margin-right: 1rem; color: var(--secondary-color);"></i>Modern Frontend Frameworks</h2>
                <p style="font-size: 1.2rem; margin-bottom: 3rem; color: var(--text-secondary);">
                    Powerful frameworks that accelerate development and improve code organization
                </p>
                <div class="tech-grid">
                    <div class="tech-card fragment rotate-in" data-fragment-index="1">
                        <i class="fab fa-react" style="color: #61dafb;"></i>
                        <h3>React</h3>
                        <p>Component-based library for building user interfaces with a virtual DOM and rich ecosystem</p>
                    </div>
                    <div class="tech-card fragment rotate-in" data-fragment-index="2">
                        <i class="fab fa-vuejs" style="color: #4fc08d;"></i>
                        <h3>Vue.js</h3>
                        <p>Progressive framework with gentle learning curve and excellent developer experience</p>
                    </div>
                    <div class="tech-card fragment rotate-in" data-fragment-index="3">
                        <i class="fab fa-angular" style="color: #dd0031;"></i>
                        <h3>Angular</h3>
                        <p>Full-featured platform with TypeScript, dependency injection, and powerful CLI</p>
                    </div>
                    <div class="tech-card fragment rotate-in" data-fragment-index="4">
                        <i class="fas fa-bolt" style="color: #ff6b35;"></i>
                        <h3>Svelte</h3>
                        <p>Compile-time framework that generates vanilla JavaScript with no runtime overhead</p>
                    </div>
                </div>
            </section>

            <!-- Responsive Design -->
            <section>
                <h2><i class="fas fa-mobile-alt"></i> Responsive Web Design</h2>
                <div class="highlight-box">
                    <ul>
                        <li>Mobile-first approach</li>
                        <li>Flexible grid systems</li>
                        <li>Media queries</li>
                        <li>Flexible images and media</li>
                    </ul>
                </div>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim>
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
}
                    </code></pre>
                </div>
            </section>

            <!-- Web Performance -->
            <section>
                <h2><i class="fas fa-tachometer-alt"></i> Web Performance</h2>
                <div class="highlight-box">
                    <ul>
                        <li>Optimize images and assets</li>
                        <li>Minimize HTTP requests</li>
                        <li>Use CDNs</li>
                        <li>Code splitting and lazy loading</li>
                        <li>Caching strategies</li>
                    </ul>
                </div>
            </section>

            <!-- Security Best Practices -->
            <section>
                <h2><i class="fas fa-shield-alt"></i> Web Security</h2>
                <div class="highlight-box">
                    <ul>
                        <li>HTTPS everywhere</li>
                        <li>Input validation and sanitization</li>
                        <li>Authentication and authorization</li>
                        <li>CORS configuration</li>
                        <li>Regular security updates</li>
                    </ul>
                </div>
            </section>

            <!-- Development Workflow -->
            <section>
                <h2><i class="fas fa-project-diagram"></i> Development Workflow</h2>
                <div class="highlight-box">
                    <ol>
                        <li>Planning and wireframing</li>
                        <li>Setting up development environment</li>
                        <li>Version control setup</li>
                        <li>Frontend development</li>
                        <li>Backend development</li>
                        <li>Testing and debugging</li>
                        <li>Deployment</li>
                    </ol>
                </div>
            </section>

            <!-- Career Paths -->
            <section data-background-gradient="linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)">
                <h2><i class="fas fa-briefcase" style="margin-right: 1rem; color: var(--accent-color);"></i>Career Paths in Web Development</h2>
                <p style="font-size: 1.2rem; margin-bottom: 3rem; color: var(--text-secondary);">
                    Diverse opportunities in the ever-growing web development industry
                </p>
                <div style="text-align: center; margin-top: 3rem;">
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-palette" style="margin-right: 0.5rem;"></i> Frontend Developer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-server" style="margin-right: 0.5rem;"></i> Backend Developer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-layer-group" style="margin-right: 0.5rem;"></i> Full-Stack Developer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-cloud" style="margin-right: 0.5rem;"></i> DevOps Engineer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-mobile-alt" style="margin-right: 0.5rem;"></i> Mobile Developer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-pencil-ruler" style="margin-right: 0.5rem;"></i> UI/UX Designer
                    </div>
                </div>
            </section>

            <!-- Learning Resources -->
            <section>
                <h2><i class="fas fa-book-open" style="margin-right: 1rem; color: var(--secondary-color);"></i>Learning Resources</h2>
                <p style="font-size: 1.2rem; margin-bottom: 3rem; color: var(--text-secondary);">
                    Curated resources to accelerate your web development journey
                </p>
                <div class="highlight-box">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-graduation-cap" style="margin-right: 0.5rem;"></i>Free Platforms
                            </h4>
                            <ul style="list-style: none; padding: 0;">
                                <li style="margin-bottom: 0.5rem;"><strong>freeCodeCamp</strong> - Comprehensive curriculum</li>
                                <li style="margin-bottom: 0.5rem;"><strong>The Odin Project</strong> - Full-stack path</li>
                                <li style="margin-bottom: 0.5rem;"><strong>MDN Web Docs</strong> - Official documentation</li>
                            </ul>
                        </div>
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-video" style="margin-right: 0.5rem;"></i>Video Learning
                            </h4>
                            <ul style="list-style: none; padding: 0;">
                                <li style="margin-bottom: 0.5rem;"><strong>YouTube</strong> - Free tutorials</li>
                                <li style="margin-bottom: 0.5rem;"><strong>Udemy</strong> - Structured courses</li>
                                <li style="margin-bottom: 0.5rem;"><strong>Pluralsight</strong> - Professional training</li>
                            </ul>
                        </div>
                        <div class="fragment fade-in">
                            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                                <i class="fas fa-code-branch" style="margin-right: 0.5rem;"></i>Practice & Projects
                            </h4>
                            <ul style="list-style: none; padding: 0;">
                                <li style="margin-bottom: 0.5rem;"><strong>GitHub</strong> - Open source projects</li>
                                <li style="margin-bottom: 0.5rem;"><strong>CodePen</strong> - Frontend playground</li>
                                <li style="margin-bottom: 0.5rem;"><strong>Frontend Mentor</strong> - Design challenges</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Q&A -->
            <section data-background-gradient="linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%)">
                <h2><i class="fas fa-comments" style="margin-right: 1rem; color: var(--accent-color);"></i>Questions & Discussion</h2>
                <p style="font-size: 1.3rem; color: var(--text-secondary); margin: 1.5rem 0;">
                    Let's explore web development together!
                </p>
                <div style="margin: 2rem 0;">
                    <i class="fas fa-code" style="font-size: 2.5rem; margin: 0 1rem; color: var(--primary-color);"></i>
                    <i class="fas fa-laptop-code" style="font-size: 2.5rem; margin: 0 1rem; color: var(--secondary-color);"></i>
                    <i class="fas fa-mobile-alt" style="font-size: 2.5rem; margin: 0 1rem; color: var(--accent-color);"></i>
                </div>
                <p style="font-size: 1.1rem; color: var(--text-muted);">
                    Ready to start your web development journey? 🚀
                </p>
                <p style="font-size: 1rem; color: var(--text-muted); opacity: 0.8; margin-top: 1rem;">
                    Thank you for your attention!
                </p>
            </section>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/plugin/highlight/highlight.min.js"></script>
    <script>
        Reveal.initialize({
            hash: true,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            controls: true,
            progress: true,
            center: true,
            touch: true,
            loop: false,
            rtl: false,
            navigationMode: 'default',
            shuffle: false,
            fragments: true,
            fragmentInURL: false,
            embedded: false,
            help: true,
            pause: true,
            showNotes: false,
            autoPlayMedia: null,
            preloadIframes: null,
            autoAnimate: true,
            autoAnimateMatcher: null,
            autoAnimateEasing: 'ease',
            autoAnimateDuration: 1.0,
            autoAnimateUnmatched: true,
            autoAnimateStyles: [
                'opacity',
                'color',
                'background-color',
                'padding',
                'font-size',
                'line-height',
                'letter-spacing',
                'border-width',
                'border-color',
                'border-radius',
                'outline',
                'outline-offset'
            ],
            autoSlide: 0,
            autoSlideStoppable: true,
            autoSlideMethod: null,
            defaultTiming: null,
            mouseWheel: false,
            previewLinks: false,
            postMessage: true,
            postMessageEvents: false,
            focusBodyOnPageVisibilityChange: true,
            plugins: [ RevealHighlight ],
            highlight: {
                highlightOnLoad: true,
                escapeHTML: true
            },
            keyboard: {
                13: 'next', // go to the next slide when the ENTER key is pressed
                27: function() {}, // do something custom when ESC is pressed
                32: null // don't do anything when SPACE is pressed (i.e. disable a reveal.js default binding)
            }
        });

        // Add some custom interactions
        Reveal.on('slidechanged', event => {
            // Custom logic for slide changes
            console.log('Slide changed to:', event.indexh);
        });

        // Add keyboard shortcuts info
        document.addEventListener('keydown', function(event) {
            if (event.key === '?' || event.key === 'h') {
                alert('Keyboard shortcuts:\n\n' +
                      '→ / ↓ / Space: Next slide\n' +
                      '← / ↑: Previous slide\n' +
                      'Home: First slide\n' +
                      'End: Last slide\n' +
                      'Esc: Slide overview\n' +
                      'F: Fullscreen\n' +
                      'S: Speaker notes\n' +
                      'B: Pause (black screen)');
            }
        });
    </script>
</body>
</html>





