<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/black.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <title>Web Development Fundamentals</title>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            --secondary-gradient: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            --accent-color: #667eea;
            --text-light: #e2e8f0;
            --bg-dark: #0f172a;
            --card-bg: rgba(15, 23, 42, 0.8);
        }

        .reveal {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: radial-gradient(ellipse at center, #1e293b 0%, #0f172a 100%);
        }

        /* Animated background particles */
        .reveal::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.08) 0%, transparent 40%),
                radial-gradient(circle at 80% 20%, rgba(79, 172, 254, 0.08) 0%, transparent 40%),
                radial-gradient(circle at 40% 40%, rgba(118, 75, 162, 0.03) 0%, transparent 40%);
            pointer-events: none;
            z-index: -2;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { 
                background: 
                    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
            }
            50% { 
                background: 
                    radial-gradient(circle at 80% 20%, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
                    radial-gradient(circle at 20% 80%, rgba(79, 172, 254, 0.15) 0%, transparent 50%),
                    radial-gradient(circle at 60% 60%, rgba(118, 75, 162, 0.08) 0%, transparent 50%);
            }
        }
        
        .reveal h1, .reveal h2 {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            font-weight: 700;
            letter-spacing: -0.02em;
            position: relative;
        }

        .reveal h1::after, .reveal h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--secondary-gradient);
            border-radius: 2px;
        }

        .reveal h3 {
            color: #4facfe !important;
            font-size: 1.8em !important;
            font-weight: 600 !important;
            margin-bottom: 25px !important;
            text-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
            margin: 50px 0;
            perspective: 1000px;
        }
        
        .tech-card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(102, 126, 234, 0.2);
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            transform: translateY(20px) rotateX(5deg);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.3),
                0 0 0 1px rgba(255,255,255,0.05),
                inset 0 1px 0 rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .tech-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .tech-card:hover::before {
            left: 100%;
        }
        
        .tech-card:hover {
            transform: translateY(-10px) rotateX(0deg) scale(1.05);
            box-shadow: 
                0 30px 60px rgba(102, 126, 234, 0.4),
                0 0 0 1px rgba(79, 172, 254, 0.3),
                inset 0 1px 0 rgba(255,255,255,0.2);
            border-color: rgba(79, 172, 254, 0.5);
        }
        
        .tech-card i {
            font-size: 4em;
            margin-bottom: 20px;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.3));
        }

        .tech-card h3 {
            color: white !important;
            font-size: 1.5em !important;
            margin-bottom: 15px !important;
            text-shadow: none !important;
        }

        .tech-card p {
            color: var(--text-light);
            opacity: 0.8;
            font-weight: 300;
        }
        
        .code-example {
            background: linear-gradient(145deg, #1e293b, #0f172a);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 1px solid rgba(102, 126, 234, 0.3);
            box-shadow: 
                0 20px 40px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .code-example::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--secondary-gradient);
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(79, 172, 254, 0.1) 100%);
            border: 2px solid transparent;
            background-clip: padding-box;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .highlight-box::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 2px;
            background: var(--secondary-gradient);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
        }
        
        .career-path {
            background: var(--primary-gradient);
            color: white;
            padding: 20px 30px;
            border-radius: 50px;
            margin: 15px;
            display: inline-block;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            font-weight: 500;
        }

        .career-path::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .career-path:hover::before {
            left: 100%;
        }
        
        .career-path:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.5);
        }
        
        .animated-title {
            animation: titleAnimation 2s ease-out;
            font-size: 3.5em !important;
            text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
            margin-bottom: 30px !important;
        }
        
        @keyframes titleAnimation {
            0% {
                opacity: 0;
                transform: translateY(50px) scale(0.8);
                filter: blur(10px);
            }
            50% {
                transform: translateY(-10px) scale(1.05);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        /* Floating animation for icons */
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(2deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        .floating-icon {
            animation: float 3s ease-in-out infinite;
        }

        .floating-icon:nth-child(2) {
            animation-delay: 0.5s;
        }

        .floating-icon:nth-child(3) {
            animation-delay: 1s;
        }
        
        .slide-number {
            color: var(--accent-color) !important;
            font-weight: 600 !important;
        }
        
        .progress {
            color: var(--accent-color) !important;
            height: 4px !important;
        }
        
        .reveal .slides section .fragment.highlight-current-blue.current-fragment {
            background: var(--primary-gradient);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        /* Custom list styling */
        .reveal ul li {
            margin-bottom: 15px;
            position: relative;
            padding-left: 30px;
        }

        .reveal ul li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-size: 0.8em;
        }

        /* Glowing effect for important elements */
        .glow {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.6); }
            to { box-shadow: 0 0 30px rgba(79, 172, 254, 0.8); }
        }

        /* Enhanced section backgrounds */
        .reveal .slides section[data-background-gradient] {
            position: relative;
        }

        .reveal .slides section[data-background-gradient]::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(79, 172, 254, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- Title Slide -->
            <section data-background-gradient="linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)">
                <h1 class="animated-title">Web Development Fundamentals</h1>
                <h3 data-fragment-index="1" class="fragment fade-in">Building Modern Web Applications</h3>
                <p data-fragment-index="2" class="fragment fade-in" style="font-size: 1.2em; color: #e2e8f0;">
                    <i class="fas fa-user-tie"></i> Instructor: [Your Name]
                </p>
                <div data-fragment-index="3" class="fragment fade-in" style="margin-top: 60px;">
                    <i class="fab fa-html5 floating-icon" style="font-size: 3em; margin: 0 30px; color: #e34c26;"></i>
                    <i class="fab fa-css3-alt floating-icon" style="font-size: 3em; margin: 0 30px; color: #1572b6;"></i>
                    <i class="fab fa-js-square floating-icon" style="font-size: 3em; margin: 0 30px; color: #f7df1e;"></i>
                </div>
            </section>

            <!-- What is Web Development -->
            <section data-transition="zoom">
                <h2><i class="fas fa-globe"></i> What is Web Development?</h2>
                <div class="highlight-box">
                    <ul>
                        <li class="fragment highlight-current-blue">Creating websites and web applications</li>
                        <li class="fragment highlight-current-blue">Frontend (client-side) + Backend (server-side)</li>
                        <li class="fragment highlight-current-blue">User interface + Server logic + Database</li>
                    </ul>
                </div>
            </section>

            <!-- Frontend Technologies -->
            <section data-transition="slide">
                <h2><i class="fas fa-paint-brush"></i> Frontend Technologies</h2>
                <div class="tech-grid">
                    <div class="tech-card fragment zoom-in" data-fragment-index="1">
                        <i class="fab fa-html5"></i>
                        <h3>HTML</h3>
                        <p>Structure & Content</p>
                    </div>
                    <div class="tech-card fragment zoom-in" data-fragment-index="2">
                        <i class="fab fa-css3-alt"></i>
                        <h3>CSS</h3>
                        <p>Styling & Layout</p>
                    </div>
                    <div class="tech-card fragment zoom-in" data-fragment-index="3">
                        <i class="fab fa-js-square"></i>
                        <h3>JavaScript</h3>
                        <p>Interactivity & Logic</p>
                    </div>
                </div>
            </section>

            <!-- HTML Example -->
            <section data-transition="convex">
                <h2><i class="fab fa-html5"></i> HTML - Structure</h2>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim data-line-numbers="1-10|3-5|6-9">
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;My Website&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;Welcome&lt;/h1&gt;
    &lt;p&gt;This is my first webpage&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;
                    </code></pre>
                </div>
            </section>

            <!-- CSS Example -->
            <section data-transition="convex">
                <h2><i class="fab fa-css3-alt"></i> CSS - Styling</h2>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim data-line-numbers="1-5|7-11">
h1 {
    color: #333;
    font-size: 2em;
    text-align: center;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}
                    </code></pre>
                </div>
            </section>

            <!-- JavaScript Example -->
            <section data-transition="convex">
                <h2><i class="fab fa-js-square"></i> JavaScript - Interactivity</h2>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim data-line-numbers="1-4|6-8">
function greetUser() {
    const name = document.getElementById('name').value;
    alert('Hello, ' + name + '!');
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded!');
});
                    </code></pre>
                </div>
            </section>

            <!-- Backend Technologies -->
            <section data-background-gradient="linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
                <h2><i class="fas fa-server"></i> Backend Technologies</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 50px;">
                    <div class="fragment fade-right">
                        <h4 style="color: #4facfe; font-size: 1.5em;"><i class="fas fa-code"></i> Languages</h4>
                        <p style="font-size: 1.1em;">Node.js, Python, PHP, Java, C#</p>
                    </div>
                    <div class="fragment fade-left">
                        <h4 style="color: #4facfe; font-size: 1.5em;"><i class="fas fa-layer-group"></i> Frameworks</h4>
                        <p style="font-size: 1.1em;">Express, Django, Laravel, Spring</p>
                    </div>
                    <div class="fragment fade-right">
                        <h4 style="color: #4facfe; font-size: 1.5em;"><i class="fas fa-database"></i> Databases</h4>
                        <p style="font-size: 1.1em;">MySQL, PostgreSQL, MongoDB</p>
                    </div>
                    <div class="fragment fade-left">
                        <h4 style="color: #4facfe; font-size: 1.5em;"><i class="fas fa-plug"></i> APIs</h4>
                        <p style="font-size: 1.1em;">REST, GraphQL</p>
                    </div>
                </div>
            </section>

            <!-- Development Tools -->
            <section>
                <h2><i class="fas fa-tools"></i> Essential Development Tools</h2>
                <div class="highlight-box">
                    <ul>
                        <li><strong>Code Editors:</strong> VS Code, WebStorm</li>
                        <li><strong>Version Control:</strong> Git, GitHub</li>
                        <li><strong>Package Managers:</strong> npm, yarn</li>
                        <li><strong>Build Tools:</strong> Webpack, Vite</li>
                        <li><strong>Testing:</strong> Jest, Cypress</li>
                    </ul>
                </div>
            </section>

            <!-- Modern Frameworks -->
            <section data-transition="zoom">
                <h2><i class="fas fa-rocket"></i> Modern Frontend Frameworks</h2>
                <div class="tech-grid">
                    <div class="tech-card fragment rotate-in" data-fragment-index="1">
                        <i class="fab fa-react"></i>
                        <h3>React</h3>
                        <p>Component-based UI</p>
                    </div>
                    <div class="tech-card fragment rotate-in" data-fragment-index="2">
                        <i class="fab fa-vuejs"></i>
                        <h3>Vue.js</h3>
                        <p>Progressive framework</p>
                    </div>
                    <div class="tech-card fragment rotate-in" data-fragment-index="3">
                        <i class="fab fa-angular"></i>
                        <h3>Angular</h3>
                        <p>Full-featured platform</p>
                    </div>
                </div>
            </section>

            <!-- Responsive Design -->
            <section>
                <h2><i class="fas fa-mobile-alt"></i> Responsive Web Design</h2>
                <div class="highlight-box">
                    <ul>
                        <li>Mobile-first approach</li>
                        <li>Flexible grid systems</li>
                        <li>Media queries</li>
                        <li>Flexible images and media</li>
                    </ul>
                </div>
                <div class="code-example fragment fade-in">
                    <pre><code data-trim>
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
}
                    </code></pre>
                </div>
            </section>

            <!-- Web Performance -->
            <section>
                <h2><i class="fas fa-tachometer-alt"></i> Web Performance</h2>
                <div class="highlight-box">
                    <ul>
                        <li>Optimize images and assets</li>
                        <li>Minimize HTTP requests</li>
                        <li>Use CDNs</li>
                        <li>Code splitting and lazy loading</li>
                        <li>Caching strategies</li>
                    </ul>
                </div>
            </section>

            <!-- Security Best Practices -->
            <section>
                <h2><i class="fas fa-shield-alt"></i> Web Security</h2>
                <div class="highlight-box">
                    <ul>
                        <li>HTTPS everywhere</li>
                        <li>Input validation and sanitization</li>
                        <li>Authentication and authorization</li>
                        <li>CORS configuration</li>
                        <li>Regular security updates</li>
                    </ul>
                </div>
            </section>

            <!-- Development Workflow -->
            <section>
                <h2><i class="fas fa-project-diagram"></i> Development Workflow</h2>
                <div class="highlight-box">
                    <ol>
                        <li>Planning and wireframing</li>
                        <li>Setting up development environment</li>
                        <li>Version control setup</li>
                        <li>Frontend development</li>
                        <li>Backend development</li>
                        <li>Testing and debugging</li>
                        <li>Deployment</li>
                    </ol>
                </div>
            </section>

            <!-- Career Paths -->
            <section data-background-gradient="linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)">
                <h2><i class="fas fa-briefcase"></i> Career Paths</h2>
                <div style="text-align: center; margin-top: 60px;">
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-palette"></i> Frontend Developer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-server"></i> Backend Developer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-layer-group"></i> Full-Stack Developer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-cloud"></i> DevOps Engineer
                    </div>
                    <div class="career-path fragment bounce-in">
                        <i class="fas fa-pencil-ruler"></i> Web Designer
                    </div>
                </div>
            </section>

            <!-- Learning Resources -->
            <section>
                <h2><i class="fas fa-book-open"></i> Learning Resources</h2>
                <div class="highlight-box">
                    <ul>
                        <li>MDN Web Docs</li>
                        <li>freeCodeCamp</li>
                        <li>The Odin Project</li>
                        <li>Codecademy</li>
                        <li>YouTube tutorials</li>
                        <li>GitHub projects</li>
                    </ul>
                </div>
            </section>

            <!-- Q&A -->
            <section data-background-gradient="linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)">
                <div style="padding: 40px 20px;">
                    <h1 class="animated-title"><i class="fas fa-comments"></i> Questions & Discussion</h1>
                    <p class="fragment fade-in" style="font-size: 1.5em; color: #e2e8f0; margin: 30px 0;">
                        Let's explore web development together!
                    </p>
                    <div class="fragment fade-in" style="margin-top: 40px;">
                        <i class="fas fa-code glow" style="font-size: 3em; margin: 0 30px; animation: pulse 2s infinite;"></i>
                        <i class="fas fa-laptop-code glow" style="font-size: 3em; margin: 0 30px; animation: pulse 2s infinite 0.5s;"></i>
                        <i class="fas fa-mobile-alt glow" style="font-size: 3em; margin: 0 30px; animation: pulse 2s infinite 1s;"></i>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/plugin/highlight/highlight.min.js"></script>
    <script>
        Reveal.initialize({
            hash: true,
            transition: 'slide',
            transitionSpeed: 'default',
            backgroundTransition: 'fade',
            plugins: [ RevealHighlight ],
            highlight: {
                highlightOnLoad: true
            }
        });
    </script>
</body>
</html>





